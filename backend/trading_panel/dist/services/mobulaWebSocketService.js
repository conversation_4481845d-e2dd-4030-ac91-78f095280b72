"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MobulaWebSocketService = void 0;
const ws_1 = __importDefault(require("ws"));
class MobulaWebSocketService {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000;
        this.currentPoolAddress = null;
        this.processedTradeIds = new Set();
        this.onTradeCallback = null;
        this.onErrorCallback = null;
        this.onConnectionCallback = null;
        // SOL address constant
        this.SOL_ADDRESS = 'So11111111111111111111111111111111111111112';
        this.MOBULA_API_KEY = process.env.MOBULA_API_KEY;
        this.MOBULA_WS_URL = 'wss://api.mobula.io';
        console.log('🔧 MobulaWebSocketService initialized');
        console.log('🔑 [BACKEND] API Key status:', {
            hasApiKey: !!this.MOBULA_API_KEY,
            apiKeyLength: this.MOBULA_API_KEY ? this.MOBULA_API_KEY.length : 0,
            apiKeyPrefix: this.MOBULA_API_KEY ? this.MOBULA_API_KEY.substring(0, 8) + '...' : 'NOT_SET'
        });
        if (!this.MOBULA_API_KEY) {
            console.warn('⚠️ [BACKEND] MOBULA_API_KEY not found in environment variables');
        }
    }
    // Set callbacks
    onTrade(callback) {
        this.onTradeCallback = callback;
    }
    onError(callback) {
        this.onErrorCallback = callback;
    }
    onConnection(callback) {
        this.onConnectionCallback = callback;
    }
    // Connect to Mobula WebSocket
    async connect(poolAddress) {
        if (this.currentPoolAddress === poolAddress && this.isConnected) {
            console.log(`📡 Already connected to ${poolAddress}`);
            return;
        }
        // Disconnect existing connection
        if (this.ws) {
            this.disconnect();
        }
        this.currentPoolAddress = poolAddress;
        console.log(`📡 Connecting to Mobula API for pool: ${poolAddress}`);
        try {
            this.ws = new ws_1.default(this.MOBULA_WS_URL);
            this.ws.on('open', () => {
                console.log(`✅ Connected to Mobula API`);
                this.isConnected = true;
                this.reconnectAttempts = 0;
                // Subscribe to the pool
                this.subscribe(poolAddress);
                // Fetch historical trades
                this.fetchHistoricalTrades(poolAddress);
                if (this.onConnectionCallback) {
                    this.onConnectionCallback(true);
                }
            });
            this.ws.on('message', (data) => {
                try {
                    const rawData = data.toString();
                    console.log('📨 [BACKEND] Raw Mobula WebSocket data:', {
                        dataLength: rawData.length,
                        dataPreview: rawData.substring(0, 200) + (rawData.length > 200 ? '...' : '')
                    });
                    const message = JSON.parse(rawData);
                    this.handleMessage(message);
                }
                catch (error) {
                    console.error('❌ [BACKEND] Error parsing Mobula WebSocket message:', {
                        error: error.message,
                        rawDataType: typeof data,
                        rawDataLength: data.toString().length,
                        rawDataSample: data.toString().substring(0, 100)
                    });
                    // Send parsing error to frontend
                    if (this.onErrorCallback) {
                        this.onErrorCallback(`JSON parsing error: ${error.message}`);
                    }
                }
            });
            this.ws.on('close', (code) => {
                console.log(`🔌 Mobula connection closed. Code: ${code}`);
                this.isConnected = false;
                if (this.onConnectionCallback) {
                    this.onConnectionCallback(false);
                }
                // Attempt reconnection
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.scheduleReconnect();
                }
            });
            this.ws.on('error', (error) => {
                console.error('❌ Mobula WebSocket error:', error);
                this.isConnected = false;
                if (this.onErrorCallback) {
                    this.onErrorCallback(error.message);
                }
            });
        }
        catch (error) {
            console.error('❌ Failed to create WebSocket connection:', error);
            if (this.onErrorCallback) {
                this.onErrorCallback(error.message);
            }
        }
    }
    // Subscribe to pool trades
    subscribe(poolAddress) {
        if (!this.ws || this.ws.readyState !== ws_1.default.OPEN) {
            console.error('❌ WebSocket not connected, cannot subscribe');
            return;
        }
        const subscriptionMessage = {
            type: 'pair',
            authorization: this.MOBULA_API_KEY,
            payload: {
                blockchain: 'solana',
                address: poolAddress,
            }
        };
        try {
            this.ws.send(JSON.stringify(subscriptionMessage));
            console.log(`✅ Subscribed to Mobula feed for ${poolAddress}`);
        }
        catch (error) {
            console.error('❌ Subscribe failed:', error.message);
            if (this.onErrorCallback) {
                this.onErrorCallback(`Failed to subscribe: ${error.message}`);
            }
        }
    }
    // Handle incoming messages
    handleMessage(message) {
        try {
            // Log the raw message structure for debugging
            console.log('📨 [BACKEND] Raw Mobula message received:', {
                messageType: typeof message,
                isArray: Array.isArray(message),
                hasType: !!message?.type,
                hasEvent: !!message?.event,
                hasData: !!message?.data,
                messageKeys: message && typeof message === 'object' ? Object.keys(message) : [],
                messagePreview: JSON.stringify(message).substring(0, 200)
            });
            // Handle different message types from Mobula
            if (message?.event === 'error') {
                console.error('❌ [BACKEND] Mobula WebSocket error:', message);
                if (this.onErrorCallback) {
                    this.onErrorCallback(`Mobula error: ${message.data || 'Unknown error'}`);
                }
                return;
            }
            // Handle connection/status messages
            if (message?.event && message.event !== 'trade') {
                console.log('📡 [BACKEND] Mobula status message:', message);
                return;
            }
            const processTrade = (trade) => {
                // Validate trade object structure
                if (!trade || typeof trade !== 'object') {
                    console.warn('⚠️ [BACKEND] Invalid trade object:', trade);
                    return;
                }
                // Check if this looks like a valid trade
                if (!trade.type || !['buy', 'sell'].includes(trade.type)) {
                    console.warn('⚠️ [BACKEND] Invalid trade type:', trade.type);
                    return;
                }
                console.log('📊 [BACKEND] Received raw trade data:', {
                    type: trade.type,
                    timestamp: trade.timestamp,
                    token_price: trade.token_price,
                    hash: trade.hash || trade.transaction_hash || trade.tx_hash,
                    hasPairData: !!trade.pairData,
                    pairDataKeys: trade.pairData ? Object.keys(trade.pairData) : [],
                    pairDataLiquidity: trade.pairData?.liquidity,
                    liquidityType: typeof trade.pairData?.liquidity,
                    allTradeKeys: Object.keys(trade)
                });
                // Specific liquidity debugging
                if (trade.pairData?.liquidity) {
                    console.log('💧 [BACKEND] LIQUIDITY FOUND in raw trade:', {
                        liquidityValue: trade.pairData.liquidity,
                        liquidityType: typeof trade.pairData.liquidity,
                        isNumber: typeof trade.pairData.liquidity === 'number',
                        isValidNumber: !isNaN(Number(trade.pairData.liquidity))
                    });
                }
                else {
                    console.log('❌ [BACKEND] NO LIQUIDITY in raw trade data');
                }
                const formattedTrade = this.formatTrade(trade);
                if (formattedTrade && this.onTradeCallback) {
                    console.log('📊 [BACKEND] Sending formatted trade with liquidity:', {
                        hasFormattedPairData: !!formattedTrade.pairData,
                        formattedLiquidity: formattedTrade.pairData?.liquidity,
                        liquidityPreserved: formattedTrade.pairData?.liquidity === trade.pairData?.liquidity,
                        preservedFields: Object.keys(formattedTrade),
                        fullFormattedPairData: formattedTrade.pairData
                    });
                    // Specific check for liquidity preservation
                    if (trade.pairData?.liquidity && formattedTrade.pairData?.liquidity) {
                        console.log('✅ [BACKEND] LIQUIDITY SUCCESSFULLY PRESERVED:', {
                            original: trade.pairData.liquidity,
                            formatted: formattedTrade.pairData.liquidity,
                            match: trade.pairData.liquidity === formattedTrade.pairData.liquidity
                        });
                    }
                    else if (trade.pairData?.liquidity && !formattedTrade.pairData?.liquidity) {
                        console.log('❌ [BACKEND] LIQUIDITY LOST DURING FORMATTING:', {
                            originalLiquidity: trade.pairData.liquidity,
                            formattedPairData: formattedTrade.pairData
                        });
                    }
                    this.onTradeCallback(formattedTrade);
                }
            };
            // Process single trade or array of trades
            if (Array.isArray(message)) {
                console.log(`📊 [BACKEND] Processing ${message.length} trades from array`);
                message.forEach(processTrade);
            }
            else if (message && typeof message === 'object') {
                console.log('📊 [BACKEND] Processing single trade object');
                processTrade(message);
            }
            else {
                console.warn('⚠️ [BACKEND] Unrecognized message format:', {
                    messageType: typeof message,
                    isArray: Array.isArray(message),
                    message: message
                });
                // Don't send error to frontend for unrecognized messages
                // These might be status messages, heartbeats, etc.
                return;
            }
        }
        catch (error) {
            console.error('❌ [BACKEND] Error handling WebSocket message:', {
                error: error.message,
                errorStack: error.stack,
                messageType: typeof message,
                messagePreview: JSON.stringify(message).substring(0, 100)
            });
            // Only send critical errors to frontend, not processing warnings
            if (error.message.includes('JSON') || error.message.includes('parse')) {
                if (this.onErrorCallback) {
                    this.onErrorCallback(`Message parsing error: ${error.message}`);
                }
            }
        }
    }
    // Fetch historical trades
    async fetchHistoricalTrades(poolAddress) {
        console.log(`📡 [BACKEND] Fetching historical trades for ${poolAddress}...`);
        try {
            const apiUrl = `https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=solana&address=${poolAddress}&limit=50`;
            console.log(`📡 [BACKEND] Making API request to: ${apiUrl}`);
            const response = await fetch(apiUrl);
            console.log(`📡 [BACKEND] API Response status: ${response.status} ${response.statusText}`);
            if (!response.ok) {
                console.warn(`⚠️ [BACKEND] Historical trades API returned ${response.status}, continuing with WebSocket only`);
                return; // Don't throw error for non-critical historical data
            }
            const result = await response.json();
            console.log(`📡 [BACKEND] API Response structure:`, {
                hasData: !!result.data,
                dataType: typeof result.data,
                isArray: Array.isArray(result.data),
                dataLength: Array.isArray(result.data) ? result.data.length : 'N/A',
                responseKeys: Object.keys(result),
                sampleData: result.data ? result.data.slice(0, 1) : null
            });
            if (result.data && Array.isArray(result.data)) {
                console.log(`📈 [BACKEND] Received ${result.data.length} historical trades`);
                if (result.data.length > 0) {
                    // Log first trade structure for liquidity debugging
                    console.log(`📊 [BACKEND] Sample historical trade structure:`, {
                        firstTrade: result.data[0],
                        hasPairData: !!result.data[0]?.pairData,
                        pairDataLiquidity: result.data[0]?.pairData?.liquidity,
                        tradeKeys: Object.keys(result.data[0] || {})
                    });
                }
                // Send trades in chronological order (oldest first)
                const trades = result.data.reverse();
                trades.forEach((trade) => {
                    const formattedTrade = this.formatTrade(trade);
                    if (formattedTrade && this.onTradeCallback) {
                        this.onTradeCallback(formattedTrade);
                    }
                });
            }
            else {
                console.warn(`⚠️ [BACKEND] Unexpected API response format, continuing with WebSocket only:`, {
                    hasData: !!result.data,
                    dataType: typeof result.data,
                    isArray: Array.isArray(result.data),
                    fullResponse: result
                });
                // Don't throw error - historical trades are not critical for live functionality
            }
        }
        catch (error) {
            console.warn('⚠️ [BACKEND] Historical trades failed (non-critical):', {
                error: error.message,
                poolAddress
            });
            // Don't propagate historical trade errors to frontend - they're not critical
            // WebSocket will continue to work for live trades
        }
    }
    // Schedule reconnection
    scheduleReconnect() {
        this.reconnectAttempts++;
        console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);
        setTimeout(() => {
            if (this.currentPoolAddress) {
                this.connect(this.currentPoolAddress);
            }
        }, this.reconnectDelay);
    }
    // Disconnect from WebSocket
    disconnect() {
        if (this.ws) {
            try {
                if (this.ws.readyState === ws_1.default.OPEN || this.ws.readyState === ws_1.default.CONNECTING) {
                    this.ws.close(1000, 'Manual disconnect');
                }
            }
            catch (error) {
                console.error('❌ Error closing WebSocket:', error);
            }
            this.ws = null;
        }
        this.isConnected = false;
        this.currentPoolAddress = null;
        this.processedTradeIds.clear();
        if (this.onConnectionCallback) {
            this.onConnectionCallback(false);
        }
    }
    // Get connection status
    getConnectionStatus() {
        return {
            connected: this.isConnected,
            poolAddress: this.currentPoolAddress
        };
    }
    // Helper methods (migrated from frontend)
    getTradeTimestamp(trade) {
        let timestamp = 0;
        if (trade.date && typeof trade.date === 'number') {
            timestamp = trade.date;
        }
        else if (trade.timestamp && typeof trade.timestamp === 'number') {
            timestamp = trade.timestamp;
        }
        else {
            timestamp = Date.now();
        }
        // Ensure timestamp is in milliseconds
        if (timestamp < 946684800000) { // Jan 1, 2000 in milliseconds
            timestamp = timestamp * 1000;
        }
        return timestamp;
    }
    getTokenPair(trade) {
        if (!trade.pairData?.token0 || !trade.pairData?.token1) {
            return { solToken: null, actualToken: null };
        }
        const token0 = trade.pairData.token0;
        const token1 = trade.pairData.token1;
        if (token1.address === this.SOL_ADDRESS) {
            return { solToken: token1, actualToken: token0 };
        }
        else if (token0.address === this.SOL_ADDRESS) {
            return { solToken: token0, actualToken: token1 };
        }
        else {
            return { solToken: token0, actualToken: token1 };
        }
    }
    generateTradeId(trade) {
        if (trade.id)
            return `id-${trade.id}`;
        if (trade.hash || trade.transaction_hash || trade.tx_hash) {
            const hash = trade.hash || trade.transaction_hash || trade.tx_hash;
            return `hash-${hash}`;
        }
        const timestamp = trade.timestamp || Date.now();
        const price = trade.price || trade.token_price || 0;
        const amount = trade.token_amount || trade.amount || 0;
        return `${timestamp}-${price}-${amount}`;
    }
    formatTrade(trade) {
        const tradeId = this.generateTradeId(trade);
        // Check for duplicates
        if (this.processedTradeIds.has(tradeId)) {
            return null;
        }
        this.processedTradeIds.add(tradeId);
        const { actualToken } = this.getTokenPair(trade);
        const marketCap = actualToken?.marketCap || trade.marketCap || 0;
        const tokenAmount = trade.token_amount_vs || trade.token_amount || trade.amount || 0;
        const actualTokenAmount = trade.token_amount || trade.amount || 0;
        const tokenAmountUsd = trade.token_amount_usd || trade.usd_amount || trade.value_usd || 0;
        const price = actualToken?.price || trade.token_price || trade.price || 0;
        const normalizedTimestamp = this.getTradeTimestamp(trade);
        // Create the base formatted trade object
        const formattedTrade = {
            id: tradeId,
            timestamp: normalizedTimestamp,
            type: trade.type,
            amount: this.formatTokenAmount(tokenAmount),
            usdAmount: `$${this.formatTradeValue(trade)}`,
            mc: `$${this.formatMarketCap(marketCap)}`,
            price: `$${(price || 0).toFixed(6)}`,
            trader: this.formatTraderHash(trade),
            age: this.formatAge(normalizedTimestamp),
            txHash: this.getFullHash(trade),
            marketCap,
            tokenAmount,
            tokenAmountUsd,
            actualTokenAmount,
            // Add poolAddress for frontend routing
            poolAddress: this.currentPoolAddress || '',
            // Preserve original trade data for liquidity and other fields
            pairData: trade.pairData,
            token_price: trade.token_price
        };
        // Mobula already provides comprehensive volume metrics in pairData
        // Log the volume metrics for debugging
        if (formattedTrade.pairData) {
            console.log('📊 [BACKEND] Mobula volume metrics available:', {
                tradeId,
                volume_5min: formattedTrade.pairData.volume_5min,
                volume_1h: formattedTrade.pairData.volume_1h,
                volume_6h: formattedTrade.pairData.volume_6h,
                volume_24h: formattedTrade.pairData.volume24h,
                buy_volume_5min: formattedTrade.pairData.buy_volume_5min,
                sell_volume_5min: formattedTrade.pairData.sell_volume_5min,
                buyers_24h: formattedTrade.pairData.buyers_24h,
                sellers_24h: formattedTrade.pairData.sellers_24h
            });
        }
        // Add additional fields from original trade without overwriting core fields
        const coreFields = new Set(['id', 'timestamp', 'type', 'amount', 'usdAmount', 'mc', 'price', 'trader', 'age', 'txHash', 'marketCap', 'tokenAmount', 'tokenAmountUsd', 'actualTokenAmount', 'pairData', 'token_price']);
        Object.keys(trade).forEach(key => {
            if (!coreFields.has(key)) {
                formattedTrade[key] = trade[key];
            }
        });
        return formattedTrade;
    }
    formatTokenAmount(tokenAmount) {
        // Handle null/undefined values
        const amount = tokenAmount || 0;
        let formattedAmount = '';
        if (amount >= 1e12) {
            formattedAmount = (amount / 1e12).toFixed(3) + 'T';
        }
        else if (amount >= 1e9) {
            formattedAmount = (amount / 1e9).toFixed(3) + 'B';
        }
        else if (amount >= 1e6) {
            formattedAmount = (amount / 1e6).toFixed(3) + 'M';
        }
        else if (amount >= 1e3) {
            formattedAmount = (amount / 1e3).toFixed(3) + 'K';
        }
        else {
            formattedAmount = amount.toLocaleString('en-US', {
                minimumFractionDigits: 3,
                maximumFractionDigits: 3
            });
        }
        return formattedAmount;
    }
    formatTradeValue(trade) {
        let usdValue = 0;
        if (trade.token_amount_usd && typeof trade.token_amount_usd === 'number') {
            usdValue = trade.token_amount_usd;
        }
        else if (trade.usd_amount && typeof trade.usd_amount === 'number') {
            usdValue = trade.usd_amount;
        }
        else if (trade.value_usd && typeof trade.value_usd === 'number') {
            usdValue = trade.value_usd;
        }
        else {
            const price = trade.price || trade.token_price || 0;
            const amount = trade.token_amount || trade.amount || 0;
            usdValue = (price || 0) * (amount || 0);
        }
        return (usdValue || 0).toFixed(2);
    }
    formatMarketCap(marketCap) {
        // Handle null/undefined values
        const cap = marketCap || 0;
        if (cap >= 1e12) {
            return (cap / 1e12).toFixed(2) + 'T';
        }
        else if (cap >= 1e9) {
            return (cap / 1e9).toFixed(2) + 'B';
        }
        else if (cap >= 1e6) {
            return (cap / 1e6).toFixed(2) + 'M';
        }
        else if (cap >= 1e3) {
            return (cap / 1e3).toFixed(2) + 'K';
        }
        else {
            return cap.toFixed(2);
        }
    }
    formatAge(timestamp) {
        const now = Date.now();
        const diffSeconds = Math.floor((now - timestamp) / 1000);
        if (isNaN(diffSeconds) || diffSeconds < 0) {
            return 'unknown';
        }
        if (diffSeconds < 1) {
            return 'just now';
        }
        else if (diffSeconds < 60) {
            return `${diffSeconds}s ago`;
        }
        else if (diffSeconds < 3600) {
            return `${Math.floor(diffSeconds / 60)}m ago`;
        }
        else if (diffSeconds < 86400) {
            return `${Math.floor(diffSeconds / 3600)}h ago`;
        }
        else {
            return `${Math.floor(diffSeconds / 86400)}d ago`;
        }
    }
    formatTraderHash(trade) {
        let hash = '';
        if (trade.hash) {
            hash = trade.hash;
        }
        else if (trade.transaction_hash) {
            hash = trade.transaction_hash;
        }
        else if (trade.tx_hash) {
            hash = trade.tx_hash;
        }
        if (!hash) {
            return 'N/A';
        }
        return `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
    }
    getFullHash(trade) {
        return trade.hash || trade.transaction_hash || trade.tx_hash || '';
    }
}
exports.MobulaWebSocketService = MobulaWebSocketService;
//# sourceMappingURL=mobulaWebSocketService.js.map